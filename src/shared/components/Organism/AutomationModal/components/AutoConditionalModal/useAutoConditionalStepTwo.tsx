import React from 'react';
import EnhancedTemplateForm from '@shared/components/Organism/AutomationModal/components/TemplateForm/EnhancedTemplateForm';
import { useTemplateActions } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateActions';
import { useTemplateForm } from '@shared/components/Organism/AutomationModal/components/TemplateForm/useTemplateForm';
import { TwoButtonFooter } from '@shared/components/Organism/MultiStepForm/ProfileSections/Components/TwoButtonFooter';
import useResponseToast from '@shared/hooks/useResponseToast';
import useTranslation from '@shared/utils/hooks/useTranslation';
import type { TemplateFormData } from '@shared/components/Organism/AutomationModal/types/template.types';
import type { MultiStepFormProps } from '@shared/components/Organism/MultiStepForm/MultiStepForm';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
  getStepHeaderProps?: Exclude<
    MultiStepFormProps['getStepHeaderProps'],
    undefined
  >;
};

interface UseAutoMoveRequirementsStepTwoProps {
  onTemplateCreated: (templateId: number) => void;
  templateFormValue: TemplateFormData | undefined;
}

export function useAutoConditionalStepTwo({
  onTemplateCreated,
  templateFormValue,
}: UseAutoMoveRequirementsStepTwoProps): SingleDataItem[] {
  const { t } = useTranslation();
  const { handleSuccess } = useResponseToast();

  const templateActions = useTemplateActions({
    onTemplateCreated: () => {},
  });

  const getStepHeaderProps: SingleDataItem['getStepHeaderProps'] = () => ({
    title: t('message'),
    iconProps: {
      name: 'mail-response',
      type: 'fal',
    },
  });

  const templateForm = useTemplateForm({
    onFormChange: (_values, _isValid) => {},
    onSubmit: (data) => {
      templateActions.createTemplate(data, (result) => {
        // handleSuccess({
        //   message: t('auto_move_requirements_title'),
        //   title: t('auto_move_requirements_subTitle'),
        // });

        onTemplateCreated(result?.id);
      });
    },
  });

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: t('requirements'),
    hideBack: false,
    noCloseButton: true,
    backButtonProps: {
      onClick: () => setStep((prev) => prev - 1),
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <TwoButtonFooter
      submitLabel={t('save')}
      secondaryButtonLabel={t('discard')}
      disabledSubmit={templateForm.isLoading || !templateForm.canSubmit}
      onSubmitClick={() => {
        templateForm.handleSubmit();
      }}
      secondaryButtonOnClick={() => setStep((prev) => prev - 1)}
    />
  );

  const renderBody: SingleDataItem['renderBody'] = ({ setStep }) => (
    <div className="p-6 h-full bg-darkSecondary overflow-y-auto">
      <EnhancedTemplateForm
        formData={templateFormValue || templateForm.formData}
        onSubmit={templateForm.handleSubmit}
        isLoading={templateForm.isLoading}
        onChange={templateForm.handleFormChange}
        hasDefault={false}
        config={{
          showDelay: true,
          showFollowup: true,
          showAttachments: true,
        }}
        title={t('auto_reply')}
        onShowTemplates={() => setStep((prev) => prev + 1)}
      />
    </div>
  );

  const data: Array<SingleDataItem> = [
    {
      stepKey: '2',
      getHeaderProps,
      renderBody,
      getStepHeaderProps,
      renderFooter,
    },
  ];

  return data;
}
